import { EnvironmentInfo } from "./index";

/**
 * <AUTHOR>
 * @data 2025-08-07 14:26
 * @filePath extensions\cc_ecs_framework\assets\utils\bigInt\BigIntFactory.ts
 * @description BigInt工厂类
 *
 * 自动检测运行时环境的BigInt支持情况，并提供统一的创建接口。
 * 在支持BigInt的环境中使用原生实现，在不支持的环境中使用兼容实现。
 */
export class BigIntFactory {

    private static _supportsBigInt: boolean | null = null;
    // private static _cachedZero: IBigIntLike | null = null;
    // private static _cachedOne: IBigIntLike | null = null;

    /** 获取环境信息 */
    public static getEnvironmentInfo(): EnvironmentInfo {
        return {
            supportsBigInt: this.isNativeSupported(),
            environment: this.detectEnvironment(),
            jsEngine: this.detectJSEngine()
        };
    }
    /** 检测JavaScript引擎 */
    private static detectJSEngine(): string {
        try {
            // V8引擎特征检测
            if (typeof process !== 'undefined' && process.versions && process.versions.v8) {
                return `V8 ${process.versions.v8}`;
            }

            // SpiderMonkey特征检测
            if (typeof (globalThis as any).Components !== 'undefined') {
                return 'SpiderMonkey';
            }

            // JavaScriptCore特征检测
            if (typeof window !== 'undefined' && typeof (window as any).safari !== 'undefined') {
                return 'JavaScriptCore';
            }

            return 'Unknown';
        } catch {
            return 'Unknown';
        }
    }
    /** 检测运行环境 */
    private static detectEnvironment(): string {
        if (typeof window !== 'undefined') {
            // 浏览器环境
            if (typeof navigator !== 'undefined') {
                const userAgent = navigator.userAgent;

                if (userAgent.includes('Chrome')) {
                    const match = userAgent.match(/Chrome\/(\d+)/);
                    const version = match ? parseInt(match[1]) : 0;
                    return `Chrome ${version}`;
                }

                if (userAgent.includes('Firefox')) {
                    const match = userAgent.match(/Firefox\/(\d+)/);
                    const version = match ? parseInt(match[1]) : 0;
                    return `Firefox ${version}`;
                }

                if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
                    const match = userAgent.match(/Version\/(\d+)/);
                    const version = match ? parseInt(match[1]) : 0;
                    return `Safari ${version}`;
                }

                return 'Browser (Unknown)';
            }

            return 'Browser';
        } else if (typeof global !== 'undefined') {
            // Node.js环境
            if (typeof process !== 'undefined' && process.version) {
                return `Node.js ${process.version}`;
            }
            return 'Node.js';
        } else if (typeof (globalThis as any).wx !== 'undefined') {
            // 微信小程序
            return 'WeChat MiniProgram';
        } else if (typeof (globalThis as any).cc !== 'undefined') {
            // Cocos Creator
            return 'Cocos Creator';
        } else if (typeof (globalThis as any).Laya !== 'undefined') {
            // Laya引擎
            return 'Laya Engine';
        }

        return 'Unknown';
    }
    /** 检查是否支持原生BigInt */
    private static isNativeSupported(): boolean {
        if (this._supportsBigInt === null) {
            this._supportsBigInt = this.detectBigIntSupport();
        }
        return this._supportsBigInt;
    }
    /** 检测BigInt支持情况 */
    private static detectBigIntSupport(): boolean {
        try {
            // 检查BigInt构造函数是否存在
            if (typeof BigInt === 'undefined') {
                return false;
            }

            // 检查基本BigInt操作
            const test1 = BigInt(1);
            const test2 = BigInt(2);
            const result = test1 | test2;

            // 检查字面量支持
            const literal = eval('1n'); // 使用eval避免语法错误

            // 检查类型
            if (typeof result !== 'bigint' || typeof literal !== 'bigint') {
                return false;
            }

            // 检查基本运算
            const shifted = test1 << BigInt(1);
            const compared = test1 === BigInt(1);

            return typeof shifted === 'bigint' && compared === true;
        } catch (error) {
            // 任何异常都表示不支持
            return false;
        }
    }

    // // 缓存检测结果以避免重复检测





    // /**
    //  * 创建BigInt兼容值
    //  * @param value 初始值
    //  * @returns IBigIntLike实例
    //  */
    // public static create(value: number | string | bigint = 0): IBigIntLike {
    //     if (this.isNativeSupported()) {
    //         let bigintValue: bigint;

    //         if (typeof value === 'bigint') {
    //             bigintValue = value;
    //         } else if (typeof value === 'string') {
    //             bigintValue = BigInt(value);
    //         } else {
    //             bigintValue = BigInt(value);
    //         }

    //         return new NativeBigInt(bigintValue);
    //     } else {
    //         // 转换bigint类型到兼容类型
    //         let compatValue: number | string;

    //         if (typeof value === 'bigint') {
    //             compatValue = value.toString();
    //         } else {
    //             compatValue = value;
    //         }

    //         return new ArrayBigInt(compatValue);
    //     }
    // }

    // /**
    //  * 创建零值
    //  * @returns 零值的IBigIntLike实例
    //  */
    // public static zero(): IBigIntLike {
    //     if (!this._cachedZero) {
    //         this._cachedZero = this.create(0);
    //     }
    //     return this._cachedZero;
    // }

    // /**
    //  * 创建1值
    //  * @returns 1值的IBigIntLike实例
    //  */
    // public static one(): IBigIntLike {
    //     if (!this._cachedOne) {
    //         this._cachedOne = this.create(1);
    //     }
    //     return this._cachedOne;
    // }

    // /**
    //  * 从二进制字符串创建
    //  * @param binary 二进制字符串
    //  * @returns IBigIntLike实例
    //  */
    // public static fromBinaryString(binary: string): IBigIntLike {
    //     if (this.isNativeSupported()) {
    //         const value = BigInt('0b' + binary);
    //         return new NativeBigInt(value);
    //     } else {
    //         return new ArrayBigInt('0b' + binary);
    //     }
    // }

    // /**
    //  * 从十六进制字符串创建
    //  * @param hex 十六进制字符串
    //  * @returns IBigIntLike实例
    //  */
    // public static fromHexString(hex: string): IBigIntLike {
    //     if (this.isNativeSupported()) {
    //         const cleanHex = hex.replace(/^0x/i, '');
    //         const value = BigInt('0x' + cleanHex);
    //         return new NativeBigInt(value);
    //     } else {
    //         return new ArrayBigInt(hex);
    //     }
    // }
}