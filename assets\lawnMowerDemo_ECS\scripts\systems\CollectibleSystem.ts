import { Vec2 } from "cc";
import { Entity } from "db://framework/ECS/Entity";
import { EntitySystem } from "db://framework/ECS/systems/EntitySystem";
import { Matcher } from "db://framework/ECS/utils/Matcher";
import { Collectible, CollectibleType } from "../components/Collectible";
import { ColliderComponent } from "../components/ColliderComponent";
import { Transform } from "../components/Transform";
import { EntityTags } from "../EntityTags";
import { PhysicsWorld } from "../PhysicsWorld";

/**
 * <AUTHOR>
 * @data 2025-08-04 22:16
 * @filePath assets\lawnMowerDemo_ECS\scripts\systems\CollectibleSystem.ts
 * @description 
 */
export class CollectibleSystem extends EntitySystem {
    private physicsWorld: PhysicsWorld;

    constructor() {
        super(Matcher.empty().all(Transform, ColliderComponent, Collectible));
        this.physicsWorld = new PhysicsWorld();
    }

    protected process(entities: Entity[]): void {
        const playerEntities = this.scene.findEntitiesByTag(EntityTags.PLAYER);
        if (playerEntities.length === 0) return;

        const player = playerEntities[0];
        const playerTransform = player.getComponent(Transform);
        const playerCollider = player.getComponent(ColliderComponent);

        if (!playerTransform || !playerCollider) return;

        for (const collectible of entities) {
            const collectibleComponent = collectible.getComponent(Collectible);
            if (!collectibleComponent || collectibleComponent.isCollected) continue;

            const collectibleTransform = collectible.getComponent(Transform);
            const collectibleCollider = collectible.getComponent(ColliderComponent);

            if (!collectibleTransform || !collectibleCollider) continue;

            if (this.checkCollision(playerTransform, playerCollider, collectibleTransform, collectibleCollider)) {
                this.handleCollection(player, collectible, collectibleComponent);
            }
        }
    }
    private handleCollection(player: Entity, collectible: Entity, collectibleComponent: Collectible): void {
        collectibleComponent.collect();

        switch (collectibleComponent.type) {
            case CollectibleType.AIR_STRIKE:
                this.activateAirStrike(player);
                break;
        }

        collectible.destroy();
    }
    private activateAirStrike(player: Entity): void {
        this.scene.eventSystem.emit('airstrike:activate', {});
    }
    private checkCollision(playerTransform: Transform, playerCollider: ColliderComponent,
        collectibleTransform: Transform, collectibleCollider: ColliderComponent): boolean {
        const playerPos = new Vec2(playerTransform.position.x, playerTransform.position.y);
        const collectiblePos = new Vec2(collectibleTransform.position.x, collectibleTransform.position.y);

        const distance = Vec2.distance(playerPos, collectiblePos);
        const collisionDistance = (playerCollider.radius + collectibleCollider.radius);

        return distance <= collisionDistance;
    }
} 