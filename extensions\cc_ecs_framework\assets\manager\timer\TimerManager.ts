import { GlobalManager } from "../GlobalManager";
import { ITimer } from "./ITimer";
import { Timer } from "./Timer";

/**
 * <AUTHOR>
 * @data 2025-08-07 14:03
 * @filePath extensions\ecs_framework\assets\manager\timer\TimerManager.ts
 * @description 定时器管理器,允许动作的延迟和重复执行
 */
export class TimerManager extends GlobalManager {

    private _timers: Array<Timer<unknown>> = [];
    /**
     * 调度一个一次性或重复的计时器，该计时器将调用已传递的动作
     * @param timeInSeconds 延迟时间（秒）
     * @param repeats 是否重复执行，默认为false
     * @param context 回调函数的上下文，默认为null
     * @param onTime 定时器触发时的回调函数
     */
    public schedule<TContext = unknown>(timeInSeconds: number, repeats: boolean, context: TContext, onTime: (timer: ITimer<TContext>) => void): Timer<TContext> {
        let timer = new Timer<TContext>();
        timer.initialize(timeInSeconds, repeats, context, onTime);
        this._timers.push(timer as Timer<unknown>);

        return timer;
    }
    /**
     * 取消一个计时器
     * @param timer 要取消调度的计时器
     */
    public unschedule(timer: Timer<unknown>) {
        const index = this._timers.indexOf(timer);
        if (index !== -1) {
            this._timers.splice(index, 1);
        }
    }
    public update(dt: number): void {
        for (let i = this._timers.length - 1; i >= 0; i--) {
            if (this._timers[i].tick()) {
                this._timers[i].unload();
                this._timers.splice(i, 1);
            }
        }
    }

}