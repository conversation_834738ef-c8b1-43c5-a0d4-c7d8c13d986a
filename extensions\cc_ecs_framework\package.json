{"$schema": "./@types/schema/package/index.json", "package_version": 2, "name": "ecs_framework", "version": "1.0.0", "author": "oldP", "editor": ">=3.8.6", "scripts": {"preinstall": "node ./scripts/preinstall.js", "build": "tsc", "watch": "tsc -w"}, "description": "i18n:ecs_framework.description", "main": "./dist/main.js", "devDependencies": {"@cocos/creator-types": "^3.8.6", "@types/node": "^18.17.1", "typescript": "^5.8.2"}, "contributions": {"asset-db": {"mount": {"path": "./assets", "readonly": false}}}}