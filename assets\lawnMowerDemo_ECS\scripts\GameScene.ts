import { <PERSON>, director, <PERSON><PERSON>, <PERSON><PERSON>, PhysicsSystem2D } from "cc";
import { Entity } from "db://framework/ECS/Entity";
import { Scene } from "db://framework/ECS/Scene";
import { CameraTarget } from "./components/CameraTarget";
import { ColliderComponent } from "./components/ColliderComponent";
import { EnemySpawner } from "./components/EnemySpawner";
import { Health } from "./components/Health";
import { Movement } from "./components/Movement";
import { PlayerInput } from "./components/PlayerInput";
import { Transform } from "./components/Transform";
import { Weapon } from "./components/Weapon";
import { EntityTags } from "./EntityTags";
import { AirStrikeSystem } from "./systems/AirStrikeSystem";
import { AISystem } from "./systems/AISystem";
import { CameraFollowSystem } from "./systems/CameraFollowSystem";
import { CameraShakeSystem } from "./systems/CameraShakeSystem";
import { CollectibleSystem } from "./systems/CollectibleSystem";
import { CollisionSystem } from "./systems/CollisionSystem";
import { EnemySpawnSystem } from "./systems/EnemySpawnSystem";
import { HealthSystem } from "./systems/HealthSystem";
import { MovementSystem } from "./systems/MovementSystem";
import { ParticleSystem } from "./systems/ParticleSystem";
import { PhysicsSystem } from "./systems/PhysicsSystem";
import { PlayerInputSystem } from "./systems/PlayerInputSystem";
import { PowerUpSpawner } from "./systems/PowerUpSpawner";
import { ProjectileSystem } from "./systems/ProjectileSystem";
import { RenderSystem } from "./systems/RenderSystem";
import { WeaponSystem } from "./systems/WeaponSystem";

/**
 * <AUTHOR>
 * @data 2025-08-02 12:03
 * @filePath assets\lawnMowerDemo_ECS\scripts\GameScene.ts
 * @description 游戏场景
 */
export class GameScene extends Scene {

    private gameContainer: Node = null!;
    private mainCamera: Camera = null!;
    private playerEntity: Entity = null!;
    private renderSystem: RenderSystem | null = null;
    private enemySpawnSystem: EnemySpawnSystem | null = null;
    private cameraShakeSystem: CameraShakeSystem | null = null;

    protected onLoad(): void {
        this.name = "lawnMowerScene";
        this.enablePhysics();
        this.createGameContainer();

        this.addSystem(new PlayerInputSystem());
        this.addSystem(new MovementSystem());
        this.addSystem(new AISystem());
        this.addSystem(new WeaponSystem());
        this.addSystem(new ProjectileSystem());
        this.addSystem(new AirStrikeSystem());
        this.addSystem(new PowerUpSpawner());
        this.addSystem(new CollisionSystem());
        this.addSystem(new CollectibleSystem());
        this.addSystem(new HealthSystem());
        this.addSystem(new ParticleSystem());
        this.addSystem(new CameraFollowSystem());
        this.addSystem(new PhysicsSystem());

        this.renderSystem = new RenderSystem();
        this.addSystem(this.renderSystem);

        this.enemySpawnSystem = new EnemySpawnSystem();
        this.addSystem(this.enemySpawnSystem);

        this.cameraShakeSystem = new CameraShakeSystem();
        this.addSystem(this.cameraShakeSystem);

        this.setupSystemDependencies();

        this.createPlayer();
        this.createEnemySpawner();
    }
    private createEnemySpawner(): void {
        const spawnerEntity = this.createEntity("EnemySpawner");
        spawnerEntity.tag = EntityTags.SPAWNER;

        const spawner = new EnemySpawner(50.0);
        spawnerEntity.addComponent(spawner);
    }
    private createPlayer(): void {
        this.playerEntity = this.createEntity("ShooterHero");
        this.playerEntity.tag = EntityTags.PLAYER;

        const transform = new Transform(0, 0, 0);
        this.playerEntity.addComponent(transform);

        const movement = new Movement(180);
        this.playerEntity.addComponent(movement);

        const playerInput = new PlayerInput();
        this.playerEntity.addComponent(playerInput);

        const cameraTarget = new CameraTarget(100);
        this.playerEntity.addComponent(cameraTarget);

        const renderable = RenderSystem.createPlayer();
        this.playerEntity.addComponent(renderable);

        const health = new Health(999999);
        this.playerEntity.addComponent(health);

        const weapon = new Weapon(30, 16);
        weapon.bulletSpeed = 600;
        weapon.bulletLifeTime = 2;
        weapon.bulletSize = 4;
        weapon.pierceCount = 1;
        weapon.autoFire = true;
        this.playerEntity.addComponent(weapon);

        const collider = new ColliderComponent('circle');
        collider.setSize(15);
        this.playerEntity.addComponent(collider);
    }
    /** 设置系统依赖关系 */
    private setupSystemDependencies(): void {
        if (this.gameContainer) {
            this.renderSystem?.setGameContainer(this.gameContainer);

            const weaponSystem = this.getEntityProcessor(WeaponSystem);
            if (weaponSystem) {
                weaponSystem.setGameContainer(this.gameContainer);
            }
        }

        if (this.mainCamera) {
            const cameraFollowSystem = this.getEntityProcessor(CameraFollowSystem);
            if (cameraFollowSystem) {
                cameraFollowSystem.setCamera(this.mainCamera);
            }
        }
    }
    /** 启用物理系统 */
    private enablePhysics(): void {
        PhysicsSystem2D.instance.enable = true;
    }
    /** 创建游戏容器 */
    private createGameContainer(): void {
        const canvas = director.getScene()?.getChildByName("Canvas");
        if (!canvas) return;
        this.gameContainer = new Node("GameContainer");
        this.gameContainer.parent = canvas;
        this.gameContainer.layer = Layers.Enum.UI_2D;
        this.mainCamera = canvas.getChildByName("Camera")?.getComponent(Camera)!;
    }

}