import { Vec2 } from "cc";
import { Component } from "db://framework/ECS/Component";

/**
 * <AUTHOR>
 * @data 2025-08-04 22:31
 * @filePath assets\lawnMowerDemo_ECS\scripts\components\RigidBody.ts
 * @description 
 */
export class RigidBody extends Component {
    public velocity: Vec2 = new Vec2();
    public acceleration: Vec2 = new Vec2();
    public mass: number = 1;
    public drag: number = 0.98;
    public bounciness: number = 0;
    public isStatic: boolean = false;

    constructor(mass: number = 1, drag: number = 0.98) {
        super();
        this.mass = mass;
        this.drag = drag;
    }
    public addForce(force: Vec2): void {
        if (this.isStatic) return;

        this.acceleration.x += force.x / this.mass;
        this.acceleration.y += force.y / this.mass;
    }
    public integrate(deltaTime: number): void {
        if (this.isStatic) return;

        this.velocity.x += this.acceleration.x * deltaTime;
        this.velocity.y += this.acceleration.y * deltaTime;

        this.applyDrag();

        this.acceleration.set(0, 0);
    }
    public applyDrag(): void {
        this.velocity.x *= this.drag;
        this.velocity.y *= this.drag;
    }
    // public addImpulse(impulse: Vec2): void {
    //     if (this.isStatic) return;

    //     this.velocity.x += impulse.x / this.mass;
    //     this.velocity.y += impulse.y / this.mass;
    // }

} 