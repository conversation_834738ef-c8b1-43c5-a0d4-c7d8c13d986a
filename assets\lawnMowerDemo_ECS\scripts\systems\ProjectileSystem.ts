import { Entity } from "db://framework/ECS/Entity";
import { EntitySystem } from "db://framework/ECS/systems/EntitySystem";
import { Matcher } from "db://framework/ECS/utils/Matcher";
import { Time } from "db://framework/utils/Time";
import { Projectile } from "../components/Projectile";
import { Transform } from "../components/Transform";

/**
 * <AUTHOR>
 * @data 2025-08-04 21:44
 * @filePath assets\lawnMowerDemo_ECS\scripts\systems\ProjectileSystem.ts
 * @description 投射物系统 - 处理子弹移动和生命周期
 */
export class ProjectileSystem extends EntitySystem {

    constructor() {
        super(Matcher.empty().all(Transform, Projectile));
    }

    protected process(entities: Entity[]): void {
        const deltaTime = Time.deltaTime;

        for (const entity of entities) {
            const transform = entity.getComponent(Transform);
            const projectile = entity.getComponent(Projectile);

            if (!transform || !projectile) continue;

            // 更新子弹位置
            transform.position.x += projectile.velocity.x * deltaTime;
            transform.position.y += projectile.velocity.y * deltaTime;

            // 更新生命周期
            if (!projectile.updateLife(deltaTime)) {
                entity.destroy();
            }
        }
    }

}