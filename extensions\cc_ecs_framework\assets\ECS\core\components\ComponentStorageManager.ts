/**
 * <AUTHOR>
 * @data 2025-08-07 16:16
 * @filePath extensions\cc_ecs_framework\assets\ECS\components\ComponentStorageManager.ts
 * @description 组件存储管理器
 * 管理所有组件类型的存储器
 */
export class ComponentStorageManager {

    // private storages = new Map<Function, ComponentStorage<any> | SoAStorage<any>>();

    // /**
    //  * 获取或创建组件存储器（默认原始存储）
    //  * @param componentType 组件类型
    //  * @returns 组件存储器
    //  */
    // public getStorage<T extends Component>(componentType: ComponentType<T>): ComponentStorage<T> | SoAStorage<T> {
    //     let storage = this.storages.get(componentType);

    //     if (!storage) {
    //         // 检查是否启用SoA优化
    //         const enableSoA = (componentType as any).__enableSoA;

    //         if (enableSoA) {
    //             // 使用SoA优化存储
    //             storage = new SoAStorage(componentType);
    //             console.log(`[SoA] 为 ${componentType.name} 启用SoA优化（适用于大规模批量操作）`);
    //         } else {
    //             // 默认使用原始存储
    //             storage = new ComponentStorage(componentType);
    //         }

    //         this.storages.set(componentType, storage);
    //     }

    //     return storage;
    // }

    // /**
    //  * 添加组件
    //  * @param entityId 实体ID
    //  * @param component 组件实例
    //  */
    // public addComponent<T extends Component>(entityId: number, component: T): void {
    //     const componentType = component.constructor as ComponentType<T>;
    //     const storage = this.getStorage(componentType);
    //     storage.addComponent(entityId, component);
    // }

    // /**
    //  * 获取组件
    //  * @param entityId 实体ID
    //  * @param componentType 组件类型
    //  * @returns 组件实例或null
    //  */
    // public getComponent<T extends Component>(entityId: number, componentType: ComponentType<T>): T | null {
    //     const storage = this.storages.get(componentType);
    //     return storage ? storage.getComponent(entityId) : null;
    // }

    // /**
    //  * 检查实体是否有组件
    //  * @param entityId 实体ID
    //  * @param componentType 组件类型
    //  * @returns 是否有组件
    //  */
    // public hasComponent<T extends Component>(entityId: number, componentType: ComponentType<T>): boolean {
    //     const storage = this.storages.get(componentType);
    //     return storage ? storage.hasComponent(entityId) : false;
    // }

    // /**
    //  * 移除组件
    //  * @param entityId 实体ID
    //  * @param componentType 组件类型
    //  * @returns 被移除的组件或null
    //  */
    // public removeComponent<T extends Component>(entityId: number, componentType: ComponentType<T>): T | null {
    //     const storage = this.storages.get(componentType);
    //     return storage ? storage.removeComponent(entityId) : null;
    // }

    // /**
    //  * 移除实体的所有组件
    //  * @param entityId 实体ID
    //  */
    // public removeAllComponents(entityId: number): void {
    //     for (const storage of this.storages.values()) {
    //         storage.removeComponent(entityId);
    //     }
    // }

    // /**
    //  * 获取实体的组件位掩码
    //  * @param entityId 实体ID
    //  * @returns 组件位掩码
    //  */
    // public getComponentMask(entityId: number): IBigIntLike {
    //     let mask = BigIntFactory.zero();

    //     for (const [componentType, storage] of this.storages.entries()) {
    //         if (storage.hasComponent(entityId)) {
    //             const componentMask = ComponentRegistry.getBitMask(componentType as ComponentType);
    //             mask = mask.or(componentMask);
    //         }
    //     }

    //     return mask;
    // }

    // /**
    //  * 压缩所有存储器
    //  */
    // public compactAll(): void {
    //     for (const storage of this.storages.values()) {
    //         storage.compact();
    //     }
    // }

    // /**
    //  * 获取所有存储器的统计信息
    //  */
    // public getAllStats(): Map<string, any> {
    //     const stats = new Map<string, any>();

    //     for (const [componentType, storage] of this.storages.entries()) {
    //         const typeName = (componentType as any).name || 'Unknown';
    //         stats.set(typeName, storage.getStats());
    //     }

    //     return stats;
    // }

    // /**
    //  * 清空所有存储器
    //  */
    // public clear(): void {
    //     for (const storage of this.storages.values()) {
    //         storage.clear();
    //     }
    //     this.storages.clear();
    // }

}