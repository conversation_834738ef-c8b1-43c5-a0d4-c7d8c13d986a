import { IComponentIndex, IndexType } from ".";
import { BitmapComponentIndex } from "./BitmapComponentIndex";
import { HashComponentIndex } from "./HashComponentIndex";

/**
 * <AUTHOR>
 * @data 2025-08-07 16:40
 * @filePath extensions\cc_ecs_framework\assets\ECS\core\components\ComponentIndexManager.ts
 * @description 智能组件索引管理器
 *
 * 根据使用模式自动选择最优的索引策略。
 * 支持动态切换索引类型以获得最佳性能。
 */
export class ComponentIndexManager {

    private _activeIndex: IComponentIndex;
    // private _indexHistory: Map<IndexType, IndexStats> = new Map();
    // private _autoOptimize = true;
    // private _optimizationThreshold = 1000;

    constructor(initialType: IndexType = IndexType.HASH) {
        this._activeIndex = this.createIndex(initialType);
    }
    /** 创建指定类型的索引 */
    private createIndex(type: IndexType): IComponentIndex {
        switch (type) {
            case IndexType.HASH:
                return new HashComponentIndex();
            case IndexType.BITMAP:
                return new BitmapComponentIndex();
            case IndexType.SORTED:
                return new HashComponentIndex();
            default:
                return new HashComponentIndex();
        }
    }

    // /**
    //  * 添加实体到索引
    //  */
    // public addEntity(entity: Entity): void {
    //     this._activeIndex.addEntity(entity);

    //     if (this._autoOptimize && this._activeIndex.getStats().queryCount % 100 === 0) {
    //         this.checkOptimization();
    //     }
    // }

    // /**
    //  * 从索引中移除实体
    //  */
    // public removeEntity(entity: Entity): void {
    //     this._activeIndex.removeEntity(entity);
    // }

    // /**
    //  * 查询包含指定组件的实体
    //  */
    // public query(componentType: ComponentType): Set<Entity> {
    //     return this._activeIndex.query(componentType);
    // }

    // /**
    //  * 批量查询多个组件
    //  */
    // public queryMultiple(componentTypes: ComponentType[], operation: 'AND' | 'OR'): Set<Entity> {
    //     return this._activeIndex.queryMultiple(componentTypes, operation);
    // }

    // /**
    //  * 手动切换索引类型
    //  */
    // public switchIndexType(type: IndexType): void {
    //     if (type === this._activeIndex.type) return;

    //     this._indexHistory.set(this._activeIndex.type, this._activeIndex.getStats());

    //     const oldIndex = this._activeIndex;
    //     this._activeIndex = this.createIndex(type);

    //     oldIndex.clear();
    // }

    // /**
    //  * 启用/禁用自动优化
    //  */
    // public setAutoOptimize(enabled: boolean): void {
    //     this._autoOptimize = enabled;
    // }

    // /**
    //  * 获取当前索引统计信息
    //  */
    // public getStats(): IndexStats {
    //     return this._activeIndex.getStats();
    // }

    // /**
    //  * 获取所有索引类型的历史统计信息
    //  */
    // public getAllStats(): Map<IndexType, IndexStats> {
    //     const current = this._activeIndex.getStats();
    //     return new Map([
    //         ...this._indexHistory,
    //         [current.type, current]
    //     ]);
    // }

    // /**
    //  * 清空索引
    //  */
    // public clear(): void {
    //     this._activeIndex.clear();
    // }



    // /**
    //  * 检查是否需要优化索引
    //  */
    // private checkOptimization(): void {
    //     if (!this._autoOptimize) return;

    //     const stats = this._activeIndex.getStats();
    //     if (stats.queryCount < this._optimizationThreshold) return;


    //     if (stats.avgQueryTime > 1.0 && stats.type !== IndexType.HASH) {
    //         this.switchIndexType(IndexType.HASH);
    //     } else if (stats.memoryUsage > 10 * 1024 * 1024 && stats.type !== IndexType.BITMAP) {
    //         this.switchIndexType(IndexType.BITMAP);
    //     }
    // }

}