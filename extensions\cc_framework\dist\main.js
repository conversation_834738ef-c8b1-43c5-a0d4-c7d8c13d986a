"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const adm_zip_1 = __importDefault(require("adm-zip"));
const fs_1 = require("fs");
const path_1 = require("path");
/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
exports.methods = {
    /**
     * @en A method that can be triggered by message
     * @zh 通过 message 触发的方法
     */
    showLog() {
        loadNecessaryResource();
        console.log('Hello World');
    },
};
/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
function load() {
    loadNecessaryResource();
}
function loadNecessaryResource() {
    console.log("[Framework] 加载插件必要资源 start--------");
    try {
        // 构建当前项目的resources路径
        const currentResourcesPath = (0, path_1.join)(Editor.Project.path, 'assets', 'resources');
        // 插件必要资源目录
        const frameResourcesPath = (0, path_1.join)(__dirname, "..");
        // 确保目标目录存在
        if (!(0, fs_1.existsSync)(currentResourcesPath)) {
            (0, fs_1.mkdirSync)(currentResourcesPath, { recursive: true });
        }
        // 检查resources.zip是否存在
        const zipPath = (0, path_1.join)(frameResourcesPath, 'resources.zip');
        if ((0, fs_1.existsSync)(zipPath)) {
            console.log('[Framework] 检查resources.zip中的文件...');
            const zip = new adm_zip_1.default(zipPath);
            const zipEntries = zip.getEntries();
            // 检查是否需要解压
            let needExtract = false;
            // 检查每个文件是否存在
            for (const entry of zipEntries) {
                if (!entry.isDirectory) {
                    const targetPath = (0, path_1.join)(currentResourcesPath, entry.entryName);
                    if (!(0, fs_1.existsSync)(targetPath)) {
                        needExtract = true;
                        console.log(`[Framework] 文件不存在: ${entry.entryName}`);
                        break;
                    }
                }
            }
            // 如果需要解压，则解压所有文件
            if (needExtract) {
                console.log('[Framework] 开始解压resources.zip...');
                zip.extractAllTo(currentResourcesPath, true);
                console.log('[Framework] resources.zip解压完成');
            }
            else {
                console.log('[Framework] 所有文件已存在，无需解压');
            }
        }
        else {
            console.log('[Framework] resources.zip文件不存在');
        }
        console.log("[Framework] 加载插件必要资源 end--------");
    }
    catch (error) {
        console.error('[Framework] 文件处理过程中发生错误:', error);
    }
}
/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
function unload() { }
//# sourceMappingURL=data:application/json;base64,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