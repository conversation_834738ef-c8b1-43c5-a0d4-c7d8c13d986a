import { Pool } from "./Pool";
import { TieredObjectPool } from "./TieredObjectPool";

/**
 * <AUTHOR>
 * @data 2025-08-07 14:44
 * @filePath extensions\cc_ecs_framework\assets\utils\pool\PoolManager.ts
 * @description 池管理器
 * 统一管理所有对象池
 */
export class PoolManager {

    /* 单例 */
    private static _instance: PoolManager = null!;
    public static get instance(): PoolManager { return PoolManager._instance || (PoolManager._instance = new PoolManager()); }
    private constructor() { }

    private pools = new Map<string, Pool<any> | TieredObjectPool<any>>();
    private autoCompactInterval = 60000; // 60秒
    private lastCompactTime = 0;

    /** 更新池管理器（应在游戏循环中调用） */
    public update(): void {
        const now = Date.now();

        if (now - this.lastCompactTime > this.autoCompactInterval) {
            this.compactAllPools();
            this.lastCompactTime = now;
        }
    }
    /** 压缩所有池（清理碎片） */
    private compactAllPools(): void {
        // 对于标准池，可以考虑清理一些长时间未使用的对象
        // 这里简单实现为重置统计信息
        for (const pool of this.pools.values()) {
            if (pool instanceof Pool) {
                pool.resetStats();
            }
        }
    }

    // /**
    //  * 注册池
    //  * @param name 池名称
    //  * @param pool 池实例
    //  */
    // public registerPool<T extends IPoolable>(name: string, pool: Pool<T> | TieredObjectPool<T>): void {
    //     this.pools.set(name, pool);
    // }

    // /**
    //  * 获取池
    //  * @param name 池名称
    //  * @returns 池实例
    //  */
    // public getPool<T extends IPoolable>(name: string): Pool<T> | TieredObjectPool<T> | null {
    //     return this.pools.get(name) || null;
    // }



    

    // /**
    //  * 获取所有池的统计信息
    //  */
    // public getAllStats(): Map<string, any> {
    //     const stats = new Map<string, any>();

    //     for (const [name, pool] of this.pools.entries()) {
    //         if (pool instanceof Pool) {
    //             stats.set(name, pool.getStats());
    //         } else if (pool instanceof TieredObjectPool) {
    //             stats.set(name, pool.getStats());
    //         }
    //     }

    //     return stats;
    // }

    // /**
    //  * 生成性能报告
    //  */
    // public generateReport(): string {
    //     const lines: string[] = [];
    //     lines.push('=== Pool Manager Report ===');

    //     let totalMemory = 0;

    //     for (const [name, pool] of this.pools.entries()) {
    //         lines.push(`\n${name}:`);

    //         if (pool instanceof Pool) {
    //             const stats = pool.getStats();
    //             lines.push(`  Type: Standard Pool`);
    //             lines.push(`  Size: ${stats.size}/${stats.maxSize}`);
    //             lines.push(`  Hit Rate: ${(stats.hitRate * 100).toFixed(1)}%`);
    //             lines.push(`  Memory: ${(stats.estimatedMemoryUsage / 1024).toFixed(1)} KB`);
    //             totalMemory += stats.estimatedMemoryUsage;
    //         } else if (pool instanceof TieredObjectPool) {
    //             const stats = pool.getStats();
    //             lines.push(`  Type: Tiered Pool`);
    //             lines.push(`  Total Size: ${stats.totalSize}/${stats.totalMaxSize}`);
    //             lines.push(`  Hit Rate: ${(stats.hitRate * 100).toFixed(1)}%`);
    //             lines.push(`  Memory: ${(stats.totalMemoryUsage / 1024).toFixed(1)} KB`);
    //             totalMemory += stats.totalMemoryUsage;
    //         }
    //     }

    //     lines.push(`\nTotal Memory Usage: ${(totalMemory / 1024 / 1024).toFixed(2)} MB`);

    //     return lines.join('\n');
    // }

}