import { ITimer } from "./ITimer";
import { Time } from "./Time";

/**
 * <AUTHOR>
 * @data 2025-08-07 14:58
 * @filePath extensions\cc_ecs_framework\assets\manager\timer\Timer.ts
 * @description 私有类隐藏ITimer的实现
 */
export class Timer<TContext = unknown> implements ITimer<TContext> {
    public context!: TContext;
    /** 延迟时间（秒） */
    private _timeInSeconds: number = 0;
    /** 是否重复执行 */
    private _repeats: boolean = false;
    /** 定时器触发时的回调函数 */
    private _onTime!: (timer: ITimer<TContext>) => void;
    private _isDone: boolean = false;
    private _elapsedTime: number = 0;
    public stop(): void {
        this._isDone = true;
    }
    public reset(): void {
        this._elapsedTime = 0;
    }
    public getContext<T>(): T {
        return this.context as unknown as T;
    }
    public initialize(timeInsSeconds: number, repeats: boolean, context: TContext, onTime: (timer: ITimer<TContext>) => void): void {
        this._timeInSeconds = timeInsSeconds;
        this._repeats = repeats;
        this.context = context;
        this._onTime = onTime.bind(context);
    }
    public tick(): boolean {
        // 如果stop在tick之前被调用，那么isDone将为true，我们不应该再做任何事情
        if (!this._isDone && this._elapsedTime > this._timeInSeconds) {
            this._elapsedTime -= this._timeInSeconds;
            this._onTime(this);

            if (!this._isDone && !this._repeats)
                this._isDone = true;
        }

        this._elapsedTime += Time.deltaTime;

        return this._isDone;
    }
    /** 空出对象引用，以便在js需要时GC可以清理它们的引用 */
    public unload(): void {
        this.context = null as unknown as TContext;
        this._onTime = null!;
    }

    // /**
    //  * 定时器是否已完成
    //  */
    // public get isDone(): boolean {
    //     return this._isDone;
    // }

    // /**
    //  * 定时器已运行的时间
    //  */
    // public get elapsedTime(): number {
    //     return this._elapsedTime;
    // }
}