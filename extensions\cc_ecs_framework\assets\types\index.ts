/** ECS调试配置接口 */
export interface IECSDebugConfig {
    /** 是否启用调试 */
    enabled: boolean;
    /** WebSocket服务器URL */
    websocketUrl: string;
    /** 是否自动重连 */
    autoReconnect?: boolean;
    /** 数据更新间隔（毫秒）- 已弃用，使用debugFrameRate替代 */
    updateInterval?: number;
    /** 调试数据发送帧率 (60fps, 30fps, 15fps) */
    debugFrameRate?: 60 | 30 | 15;
    /** 调试数据通道 */
    channels: {
        /** 实体信息 */
        entities: boolean;
        /** 系统信息 */
        systems: boolean;
        /** 性能数据 */
        performance: boolean;
        /** 组件信息 */
        components: boolean;
        /** 场景信息 */
        scenes: boolean;
    };
}
/** Core配置接口 */
export interface ICoreConfig {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 是否启用实体系统 */
    enableEntitySystems?: boolean;
    /** 调试配置 */
    debugConfig?: IECSDebugConfig;
}
/**
 * 组件接口
 * 
 * 定义组件的基本契约，所有组件都应该实现此接口
 */
export interface IComponent {
    /** 组件唯一标识符 */
    readonly id: number;
    /** 组件所属的实体ID */
    entityId?: string | number;
    /** 组件启用状态 */
    enabled: boolean;
    /** 更新顺序 */
    updateOrder: number;

    /** 组件添加到实体时的回调 */
    onAddedToEntity?(): void;
    /** 组件从实体移除时的回调 */
    onRemovedFromEntity?(): void;
    /** 组件启用时的回调 */
    onEnabled?(): void;
    /** 组件禁用时的回调 */
    onDisabled?(): void;
    /** 更新组件 */
    update?(): void;
}