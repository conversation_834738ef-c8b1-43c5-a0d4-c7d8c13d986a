import { ComponentType, IComponentIndex, IndexStats, IndexType } from ".";
import { ECSEntity } from "../../ECSEntity";

/**
 * <AUTHOR>
 * @data 2025-08-07 16:48
 * @filePath extensions\cc_ecs_framework\assets\ECS\core\components\BitmapComponentIndex.ts
 * @description 位图索引实现
 *
 * 使用位操作进行快速集合运算，内存效率高。
 * 适合有限组件类型和大量实体的场景。
 */
export class BitmapComponentIndex implements IComponentIndex {
    public readonly type = IndexType.BITMAP;

    private _componentTypeToBit = new Map<ComponentType, number>();
    private _entityToBitmap = new Map<ECSEntity, number>();
    private _bitToEntities = new Map<number, Set<ECSEntity>>();
    private _nextBit = 0;
    private _queryCount = 0;
    private _totalQueryTime = 0;
    private _lastUpdated = Date.now();
    public addEntity(entity: ECSEntity): void {
        let bitmap = 0;

        for (const component of entity.components) {
            const componentType = component.constructor as ComponentType;
            let bit = this._componentTypeToBit.get(componentType);

            if (bit === undefined) {
                bit = this._nextBit++;
                this._componentTypeToBit.set(componentType, bit);
            }

            bitmap |= (1 << bit);

            let entities = this._bitToEntities.get(1 << bit);
            if (!entities) {
                entities = new Set();
                this._bitToEntities.set(1 << bit, entities);
            }
            entities.add(entity);
        }

        this._entityToBitmap.set(entity, bitmap);
        this._lastUpdated = Date.now();
    }
    public removeEntity(entity: ECSEntity): void {
        const bitmap = this._entityToBitmap.get(entity);
        if (bitmap === undefined) return;

        // 从所有相关的位集合中移除实体
        for (const [bitMask, entities] of this._bitToEntities) {
            if ((bitmap & bitMask) !== 0) {
                entities.delete(entity);
                if (entities.size === 0) {
                    this._bitToEntities.delete(bitMask);
                }
            }
        }

        this._entityToBitmap.delete(entity);
        this._lastUpdated = Date.now();
    }
    public query(componentType: ComponentType): Set<ECSEntity> {
        const startTime = performance.now();

        const bit = this._componentTypeToBit.get(componentType);
        if (bit === undefined) {
            this._queryCount++;
            this._totalQueryTime += performance.now() - startTime;
            return new Set();
        }

        const result = new Set(this._bitToEntities.get(1 << bit) || []);

        this._queryCount++;
        this._totalQueryTime += performance.now() - startTime;

        return result;
    }
    public queryMultiple(componentTypes: ComponentType[], operation: 'AND' | 'OR'): Set<ECSEntity> {
        const startTime = performance.now();

        if (componentTypes.length === 0) {
            return new Set();
        }

        let targetBitmap = 0;
        const validBits: number[] = [];

        for (const componentType of componentTypes) {
            const bit = this._componentTypeToBit.get(componentType);
            if (bit !== undefined) {
                targetBitmap |= (1 << bit);
                validBits.push(1 << bit);
            }
        }

        const result = new Set<ECSEntity>();

        if (operation === 'AND') {
            for (const [entity, entityBitmap] of this._entityToBitmap) {
                if ((entityBitmap & targetBitmap) === targetBitmap) {
                    result.add(entity);
                }
            }
        } else {
            for (const bitMask of validBits) {
                const entities = this._bitToEntities.get(bitMask);
                if (entities) {
                    for (const entity of entities) {
                        result.add(entity);
                    }
                }
            }
        }

        this._queryCount++;
        this._totalQueryTime += performance.now() - startTime;

        return result;
    }
    public clear(): void {
        this._componentTypeToBit.clear();
        this._entityToBitmap.clear();
        this._bitToEntities.clear();
        this._nextBit = 0;
        this._lastUpdated = Date.now();
    }
    public getStats(): IndexStats {
        let memoryUsage = 0;

        memoryUsage += this._componentTypeToBit.size * 12;
        memoryUsage += this._entityToBitmap.size * 12;
        memoryUsage += this._bitToEntities.size * 64;

        for (const entities of this._bitToEntities.values()) {
            memoryUsage += entities.size * 8;
        }

        return {
            type: this.type,
            size: this._componentTypeToBit.size,
            memoryUsage,
            queryCount: this._queryCount,
            avgQueryTime: this._queryCount > 0 ? this._totalQueryTime / this._queryCount : 0,
            lastUpdated: this._lastUpdated
        };
    }
}