import { IPoolable, PoolStats } from ".";

/**
 * <AUTHOR>
 * @data 2025-08-07 15:41
 * @filePath extensions\cc_ecs_framework\assets\utils\pool\Pool.ts
 * @description 高性能通用对象池
 * 支持任意类型的对象池化，包含详细的统计信息
 */
export class Pool<T extends IPoolable> {
    // private static _pools = new Map<Function, Pool<any>>();

    // private _objects: T[] = [];
    private _createFn: () => T; // 创建对象的函数
    private _maxSize: number; // 池的最大大小
    private _stats: PoolStats;
    private _objectSize: number; // 估算的单个对象大小

    /**
     * 构造函数
     * @param createFn 创建对象的函数
     * @param maxSize 池的最大大小，默认100
     * @param estimatedObjectSize 估算的单个对象大小（字节），默认1024
     */
    constructor(createFn: () => T, maxSize: number = 100, estimatedObjectSize: number = 1024) {
        this._createFn = createFn;
        this._maxSize = maxSize;
        this._objectSize = estimatedObjectSize;
        this._stats = {
            size: 0,
            maxSize,
            totalCreated: 0,
            totalObtained: 0,
            totalReleased: 0,
            hitRate: 0,
            estimatedMemoryUsage: 0
        };
    }
    /** 重置统计信息 */
    public resetStats(): void {
        this._stats.totalCreated = 0;
        this._stats.totalObtained = 0;
        this._stats.totalReleased = 0;
        this._stats.hitRate = 0;
    }
    // /**
    //  * 获取指定类型的对象池
    //  * @param type 对象类型
    //  * @param maxSize 池的最大大小
    //  * @param estimatedObjectSize 估算的单个对象大小
    //  * @returns 对象池实例
    //  */
    // public static getPool<T extends IPoolable>(
    //     type: new (...args: unknown[]) => T,
    //     maxSize: number = 100,
    //     estimatedObjectSize: number = 1024
    // ): Pool<T> {
    //     let pool = this._pools.get(type);

    //     if (!pool) {
    //         pool = new Pool<T>(() => new type(), maxSize, estimatedObjectSize);
    //         this._pools.set(type, pool);
    //     }

    //     return pool;
    // }

    // /**
    //  * 从池中获取对象
    //  * @returns 对象实例
    //  */
    // public obtain(): T {
    //     this._stats.totalObtained++;

    //     if (this._objects.length > 0) {
    //         const obj = this._objects.pop()!;
    //         this._stats.size--;
    //         this._updateHitRate();
    //         this._updateMemoryUsage();
    //         return obj;
    //     }

    //     // 池中没有对象，创建新的
    //     const obj = this._createFn();
    //     this._stats.totalCreated++;
    //     this._updateHitRate();
    //     return obj;
    // }

    // /**
    //  * 将对象归还到池中
    //  * @param obj 要归还的对象
    //  */
    // public free(obj: T): void {
    //     if (this._objects.length < this._maxSize) {
    //         obj.reset();
    //         this._objects.push(obj);
    //         this._stats.size++;
    //         this._stats.totalReleased++;
    //         this._updateMemoryUsage();
    //     }
    //     // 如果池已满，对象会被丢弃（由GC回收）
    // }

    // /**
    //  * 预热池，创建指定数量的对象
    //  * @param count 要创建的对象数量
    //  */
    // public warmUp(count: number): void {
    //     const targetSize = Math.min(count, this._maxSize);

    //     while (this._objects.length < targetSize) {
    //         const obj = this._createFn();
    //         this._stats.totalCreated++;
    //         this._objects.push(obj);
    //         this._stats.size++;
    //     }

    //     this._updateMemoryUsage();
    // }

    // /**
    //  * 清空池
    //  */
    // public clear(): void {
    //     this._objects.length = 0;
    //     this._stats.size = 0;
    //     this._updateMemoryUsage();
    // }

    // /**
    //  * 获取池中对象数量
    //  */
    // public get size(): number {
    //     return this._objects.length;
    // }

    // /**
    //  * 获取池的最大大小
    //  */
    // public get maxSize(): number {
    //     return this._maxSize;
    // }

    // /**
    //  * 设置池的最大大小
    //  */
    // public set maxSize(value: number) {
    //     this._maxSize = value;
    //     this._stats.maxSize = value;

    //     // 如果当前池大小超过新的最大值，则移除多余的对象
    //     while (this._objects.length > this._maxSize) {
    //         this._objects.pop();
    //         this._stats.size--;
    //     }

    //     this._updateMemoryUsage();
    // }

    // /**
    //  * 获取池的统计信息
    //  */
    // public getStats(): PoolStats {
    //     return { ...this._stats };
    // }


    // /**
    //  * 更新命中率
    //  */
    // private _updateHitRate(): void {
    //     if (this._stats.totalObtained > 0) {
    //         const hits = this._stats.totalObtained - this._stats.totalCreated;
    //         this._stats.hitRate = hits / this._stats.totalObtained;
    //     }
    // }

    // /**
    //  * 更新内存使用估算
    //  */
    // private _updateMemoryUsage(): void {
    //     this._stats.estimatedMemoryUsage = this._stats.size * this._objectSize;
    // }

    // /**
    //  * 静态方法：从指定类型的池中获取对象
    //  * @param type 对象类型
    //  * @returns 对象实例
    //  */
    // public static obtain<T extends IPoolable>(type: new (...args: unknown[]) => T): T {
    //     return this.getPool(type).obtain();
    // }

    // /**
    //  * 静态方法：将对象归还到对应类型的池中
    //  * @param type 对象类型
    //  * @param obj 要归还的对象
    //  */
    // public static free<T extends IPoolable>(type: new (...args: unknown[]) => T, obj: T): void {
    //     this.getPool(type).free(obj);
    // }

    // /**
    //  * 静态方法：预热指定类型的池
    //  * @param type 对象类型
    //  * @param count 要创建的对象数量
    //  */
    // public static warmUp<T extends IPoolable>(type: new (...args: unknown[]) => T, count: number): void {
    //     this.getPool(type).warmUp(count);
    // }

    // /**
    //  * 静态方法：清空指定类型的池
    //  * @param type 对象类型
    //  */
    // public static clearPool<T extends IPoolable>(type: new (...args: unknown[]) => T): void {
    //     const pool = this._pools.get(type);
    //     if (pool) {
    //         pool.clear();
    //     }
    // }

    // /**
    //  * 静态方法：清空所有池
    //  */
    // public static clearAllPools(): void {
    //     for (const pool of this._pools.values()) {
    //         pool.clear();
    //     }
    //     this._pools.clear();
    // }

    // /**
    //  * 静态方法：获取池的统计信息
    //  * @returns 池的统计信息
    //  */
    // public static getStats(): { [typeName: string]: PoolStats } {
    //     const stats: { [typeName: string]: PoolStats } = {};

    //     for (const [type, pool] of this._pools.entries()) {
    //         const typeName = (type as any).name || 'Unknown';
    //         stats[typeName] = pool.getStats();
    //     }

    //     return stats;
    // }

    // /**
    //  * 静态方法：获取所有池的总内存使用量
    //  * @returns 总内存使用量（字节）
    //  */
    // public static getTotalMemoryUsage(): number {
    //     let total = 0;
    //     for (const pool of this._pools.values()) {
    //         total += pool.getStats().estimatedMemoryUsage;
    //     }
    //     return total;
    // }

    // /**
    //  * 静态方法：获取性能报告
    //  * @returns 格式化的性能报告
    //  */
    // public static getPerformanceReport(): string {
    //     const stats = this.getStats();
    //     const lines: string[] = [];

    //     lines.push('=== Object Pool Performance Report ===');
    //     lines.push(`Total Memory Usage: ${(this.getTotalMemoryUsage() / 1024 / 1024).toFixed(2)} MB`);
    //     lines.push('');

    //     for (const [typeName, stat] of Object.entries(stats)) {
    //         lines.push(`${typeName}:`);
    //         lines.push(`  Size: ${stat.size}/${stat.maxSize}`);
    //         lines.push(`  Hit Rate: ${(stat.hitRate * 100).toFixed(1)}%`);
    //         lines.push(`  Total Created: ${stat.totalCreated}`);
    //         lines.push(`  Total Obtained: ${stat.totalObtained}`);
    //         lines.push(`  Memory: ${(stat.estimatedMemoryUsage / 1024).toFixed(1)} KB`);
    //         lines.push('');
    //     }

    //     return lines.join('\n');
    // }
}