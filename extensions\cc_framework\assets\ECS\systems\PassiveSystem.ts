import { Entity } from "../Entity";
import { Matcher } from "../utils/Matcher";
import { EntitySystem } from "./EntitySystem";

/**
 * <AUTHOR>
 * @data 2025-08-04 21:49
 * @filePath extensions\cc_framework\assets\ECS\systems\PassiveSystem.ts
 * @description 被动实体系统
 * 定义一个被动的实体系统，继承自EntitySystem类
 * 被动的实体系统不会对实体进行任何修改，只会被动地接收实体的变化事件
 */
export class PassiveSystem extends EntitySystem {
    constructor(matcher?: Matcher) { super(matcher) }

    /** 不进行任何处理 */
    protected override process(entities: Entity[]): void { }

}