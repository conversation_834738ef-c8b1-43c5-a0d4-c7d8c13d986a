/**
 * <AUTHOR>
 * @data 2025-08-07 16:38
 * @filePath extensions\cc_ecs_framework\assets\ECS\core\components\ComponentPoolManager.ts
 * @description 全局组件池管理器
 */
export class ComponentPoolManager {

    /* 单例 */
    private static _instance: ComponentPoolManager = null!;
    public static get instance(): ComponentPoolManager { return ComponentPoolManager._instance || (ComponentPoolManager._instance = new ComponentPoolManager()); }
    private constructor() { }

    // private pools = new Map<string, ComponentPool<any>>();

    // /**
    //  * 注册组件池
    //  */
    // registerPool<T extends Component>(
    //     componentName: string,
    //     createFn: () => T,
    //     resetFn?: (component: T) => void,
    //     maxSize?: number
    // ): void {
    //     this.pools.set(componentName, new ComponentPool(createFn, resetFn, maxSize));
    // }

    // /**
    //  * 获取组件实例
    //  */
    // acquireComponent<T extends Component>(componentName: string): T | null {
    //     const pool = this.pools.get(componentName);
    //     return pool ? pool.acquire() : null;
    // }

    // /**
    //  * 释放组件实例
    //  */
    // releaseComponent<T extends Component>(componentName: string, component: T): void {
    //     const pool = this.pools.get(componentName);
    //     if (pool) {
    //         pool.release(component);
    //     }
    // }

    // /**
    //  * 预热所有池
    //  */
    // prewarmAll(count: number = 100): void {
    //     for (const pool of this.pools.values()) {
    //         pool.prewarm(count);
    //     }
    // }

    // /**
    //  * 清空所有池
    //  */
    // clearAll(): void {
    //     for (const pool of this.pools.values()) {
    //         pool.clear();
    //     }
    // }

    // /**
    //  * 重置管理器，移除所有注册的池
    //  */
    // reset(): void {
    //     this.pools.clear();
    // }

    // /**
    //  * 获取池统计信息
    //  */
    // getPoolStats(): Map<string, { available: number; maxSize: number }> {
    //     const stats = new Map();
    //     for (const [name, pool] of this.pools) {
    //         stats.set(name, {
    //             available: pool.getAvailableCount(),
    //             maxSize: pool.getMaxSize()
    //         });
    //     }
    //     return stats;
    // }

    // /**
    //  * 获取池利用率信息（用于调试）
    //  */
    // getPoolUtilization(): Map<string, { used: number; total: number; utilization: number }> {
    //     const utilization = new Map();
    //     for (const [name, pool] of this.pools) {
    //         const available = pool.getAvailableCount();
    //         const maxSize = pool.getMaxSize();
    //         const used = maxSize - available;
    //         const utilRate = maxSize > 0 ? (used / maxSize * 100) : 0;

    //         utilization.set(name, {
    //             used: used,
    //             total: maxSize,
    //             utilization: utilRate
    //         });
    //     }
    //     return utilization;
    // }

    // /**
    //  * 获取指定组件的池利用率
    //  */
    // getComponentUtilization(componentName: string): number {
    //     const pool = this.pools.get(componentName);
    //     if (!pool) return 0;

    //     const available = pool.getAvailableCount();
    //     const maxSize = pool.getMaxSize();
    //     const used = maxSize - available;

    //     return maxSize > 0 ? (used / maxSize * 100) : 0;
    // }

}