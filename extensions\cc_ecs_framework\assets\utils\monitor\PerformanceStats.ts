/** 性能统计信息 */
export interface PerformanceStats {
    /** 总执行时间 */
    totalTime: number;
    /** 平均执行时间 */
    averageTime: number;
    /** 最小执行时间 */
    minTime: number;
    /** 最大执行时间 */
    maxTime: number;
    /** 执行次数 */
    executionCount: number;
    /** 最近的执行时间列表 */
    recentTimes: number[];
    /** 标准差 */
    standardDeviation: number;
    /** 95百分位数 */
    percentile95: number;
    /** 99百分位数 */
    percentile99: number;
}