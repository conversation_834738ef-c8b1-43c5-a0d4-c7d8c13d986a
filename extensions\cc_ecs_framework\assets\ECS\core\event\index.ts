import { IComponent } from "db://ecs_framework/types";
import { TypeSafeEventSystem } from "./TypeSafeEventSystem";

/** 事件监听器配置接口 */
export interface IEventListenerConfig {
    /** 是否只执行一次 */
    once?: boolean;
    /** 优先级（数字越大优先级越高） */
    priority?: number;
    /** 是否异步执行 */
    async?: boolean;
    /** 执行上下文 */
    context?: unknown;
}
/** 事件统计信息接口 */
export interface IEventStats {
    /** 事件类型 */
    eventType: string;
    /** 监听器数量 */
    listenerCount: number;
    /** 触发次数 */
    triggerCount: number;
    /** 总执行时间（毫秒） */
    totalExecutionTime: number;
    /** 平均执行时间（毫秒） */
    averageExecutionTime: number;
    /** 最后触发时间 */
    lastTriggerTime: number;
}
/**
 * 事件总线接口
 * 提供类型安全的事件发布订阅机制
 */
export interface IEventBus {
    /**
     * 发射事件
     * @param eventType 事件类型
     * @param data 事件数据
     */
    emit<T>(eventType: string, data: T): void;

    /**
     * 异步发射事件
     * @param eventType 事件类型
     * @param data 事件数据
     */
    emitAsync<T>(eventType: string, data: T): Promise<void>;

    /**
     * 监听事件
     * @param eventType 事件类型
     * @param handler 事件处理器
     * @param config 监听器配置
     * @returns 监听器ID
     */
    on<T>(eventType: string, handler: (data: T) => void, config?: IEventListenerConfig): string;

    /**
     * 监听事件（一次性）
     * @param eventType 事件类型
     * @param handler 事件处理器
     * @param config 监听器配置
     * @returns 监听器ID
     */
    once<T>(eventType: string, handler: (data: T) => void, config?: IEventListenerConfig): string;

    /**
     * 异步监听事件
     * @param eventType 事件类型
     * @param handler 异步事件处理器
     * @param config 监听器配置
     * @returns 监听器ID
     */
    onAsync<T>(eventType: string, handler: (data: T) => Promise<void>, config?: IEventListenerConfig): string;

    /**
     * 移除事件监听器
     * @param eventType 事件类型
     * @param listenerId 监听器ID
     */
    off(eventType: string, listenerId: string): boolean;

    /**
     * 移除指定事件类型的所有监听器
     * @param eventType 事件类型
     */
    offAll(eventType: string): void;

    /**
     * 检查是否有指定事件的监听器
     * @param eventType 事件类型
     */
    hasListeners(eventType: string): boolean;

    /**
     * 获取事件统计信息
     * @param eventType 事件类型（可选）
     */
    getStats(eventType?: string): IEventStats | Map<string, IEventStats>;

    /**
     * 清空所有监听器
     */
    clear(): void;
}
/** 事件监听器配置 */
export interface EventListenerConfig {
    /** 是否只执行一次 */
    once?: boolean;
    /** 优先级（数字越大优先级越高） */
    priority?: number;
    /** 是否异步执行 */
    async?: boolean;
    /** 执行上下文 */
    context?: any;
}
/** 事件处理器函数类型 */
export type EventHandler<T> = (event: T) => void;
/** 事件监听器配置 */
export interface EventListenerConfig {
    /** 是否只执行一次 */
    once?: boolean;
    /** 优先级（数字越大优先级越高） */
    priority?: number;
    /** 是否异步执行 */
    async?: boolean;
    /** 执行上下文 */
    context?: any;
}
/** 异步事件处理器函数类型 */
export type AsyncEventHandler<T> = (event: T) => Promise<void>;
/** 内部事件监听器 */
export interface InternalEventListener<T = any> {
    handler: EventHandler<T> | AsyncEventHandler<T>;
    config: EventListenerConfig;
    id: string;
}
/** 事件统计信息 */
export interface EventStats {
    /** 事件类型 */
    eventType: string;
    /** 监听器数量 */
    listenerCount: number;
    /** 触发次数 */
    triggerCount: number;
    /** 总执行时间（毫秒） */
    totalExecutionTime: number;
    /** 平均执行时间（毫秒） */
    averageExecutionTime: number;
    /** 最后触发时间 */
    lastTriggerTime: number;
}
/** 事件数据基类接口 */
export interface IEventData {
    /** 事件时间戳 */
    timestamp: number;
    /** 事件来源 */
    source?: string;
    /** 事件ID */
    eventId?: string;
}
/** 事件批处理配置 */
export interface EventBatchConfig {
    /** 批处理大小 */
    batchSize: number;
    /** 批处理延迟（毫秒） */
    delay: number;
    /** 是否启用批处理 */
    enabled: boolean;
}
/** 事件统计信息 */
export interface EventStats {
    /** 事件类型 */
    eventType: string;
    /** 监听器数量 */
    listenerCount: number;
    /** 触发次数 */
    triggerCount: number;
    /** 总执行时间（毫秒） */
    totalExecutionTime: number;
    /** 平均执行时间（毫秒） */
    averageExecutionTime: number;
    /** 最后触发时间 */
    lastTriggerTime: number;
}
/** 实体事件数据接口 */
export interface IEntityEventData extends IEventData {
    /** 实体ID */
    entityId: number;
    /** 实体名称 */
    entityName?: string;
    /** 实体标签 */
    entityTag?: string;
}
/** 组件事件数据接口 */
export interface IComponentEventData extends IEntityEventData {
    /** 组件类型名称 */
    componentType: string;
    /** 组件实例 */
    component?: IComponent;
}
/** 全局事件系统实例 */
export const GlobalEventSystem = new TypeSafeEventSystem();
/**
 * 事件装饰器 - 用于自动注册事件监听器
 * @param eventType 事件类型
 * @param config 监听器配置
 */
export function EventListener(eventType: string, config: EventListenerConfig = {}) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;

        // 在类实例化时自动注册监听器
        const initMethod = target.constructor.prototype.initEventListeners || function () { };
        target.constructor.prototype.initEventListeners = function () {
            initMethod.call(this);
            GlobalEventSystem.on(eventType, originalMethod.bind(this), config);
        };
    };
}

/**
 * 异步事件装饰器
 * @param eventType 事件类型
 * @param config 监听器配置
 */
export function AsyncEventListener(eventType: string, config: EventListenerConfig = {}) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;

        const initMethod = target.constructor.prototype.initEventListeners || function () { };
        target.constructor.prototype.initEventListeners = function () {
            initMethod.call(this);
            GlobalEventSystem.onAsync(eventType, originalMethod.bind(this), config);
        };
    };
} 