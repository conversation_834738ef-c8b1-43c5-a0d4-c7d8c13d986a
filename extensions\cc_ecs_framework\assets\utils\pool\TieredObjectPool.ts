import { IPoolable } from ".";
import { Pool } from "./Pool";

/**
 * <AUTHOR>
 * @data 2025-08-07 15:48
 * @filePath extensions\cc_ecs_framework\assets\utils\pool\TieredObjectPool.ts
 * @description 分层对象池
 * 使用多个不同大小的池来优化内存使用
 */
export class TieredObjectPool<T extends IPoolable> {
    private pools: Pool<T>[] = [];
    private createFn: () => T; // 创建对象的函数
    private resetFn: (obj: T) => void; // 重置对象的函数
    private tierSizes: number[]; // 各层级的大小，默认[10, 50, 200]
    // private totalObtained = 0;
    // private totalReleased = 0;

    /**
     * 构造函数
     * @param createFn 创建对象的函数
     * @param resetFn 重置对象的函数
     * @param tierSizes 各层级的大小，默认[10, 50, 200]
     * @param estimatedObjectSize 估算的单个对象大小
     */
    constructor(
        createFn: () => T,
        resetFn: (obj: T) => void,
        tierSizes: number[] = [10, 50, 200],
        estimatedObjectSize: number = 1024
    ) {
        this.createFn = createFn;
        this.resetFn = resetFn;
        this.tierSizes = tierSizes;

        // 初始化不同层级的池
        for (const size of tierSizes) {
            this.pools.push(new Pool(createFn, size, estimatedObjectSize));
        }
    }

    // /**
    //  * 获取对象
    //  * @returns 对象实例
    //  */
    // public obtain(): T {
    //     this.totalObtained++;

    //     // 从最小的池开始尝试获取
    //     for (const pool of this.pools) {
    //         if (pool.size > 0) {
    //             return pool.obtain();
    //         }
    //     }

    //     // 所有池都空了，创建新对象
    //     return this.createFn();
    // }

    // /**
    //  * 释放对象
    //  * @param obj 要释放的对象
    //  */
    // public release(obj: T): void {
    //     this.totalReleased++;
    //     this.resetFn(obj);

    //     // 放入第一个有空间的池
    //     for (const pool of this.pools) {
    //         if (pool.size < pool.maxSize) {
    //             pool.free(obj);
    //             return;
    //         }
    //     }

    //     // 所有池都满了，直接丢弃
    // }

    // /**
    //  * 预热所有池
    //  * @param totalCount 总预热数量
    //  */
    // public warmUp(totalCount: number): void {
    //     let remaining = totalCount;

    //     for (const pool of this.pools) {
    //         const warmUpCount = Math.min(remaining, pool.maxSize);
    //         pool.warmUp(warmUpCount);
    //         remaining -= warmUpCount;

    //         if (remaining <= 0) break;
    //     }
    // }

    // /**
    //  * 清空所有池
    //  */
    // public clear(): void {
    //     for (const pool of this.pools) {
    //         pool.clear();
    //     }
    // }

    // /**
    //  * 获取统计信息
    //  */
    // public getStats(): {
    //     totalSize: number;
    //     totalMaxSize: number;
    //     totalMemoryUsage: number;
    //     tierStats: PoolStats[];
    //     hitRate: number;
    // } {
    //     let totalSize = 0;
    //     let totalMaxSize = 0;
    //     let totalMemoryUsage = 0;
    //     const tierStats: PoolStats[] = [];

    //     for (const pool of this.pools) {
    //         const stats = pool.getStats();
    //         tierStats.push(stats);
    //         totalSize += stats.size;
    //         totalMaxSize += stats.maxSize;
    //         totalMemoryUsage += stats.estimatedMemoryUsage;
    //     }

    //     const hitRate = this.totalObtained > 0 ?
    //         (this.totalObtained - this.getTotalCreated()) / this.totalObtained : 0;

    //     return {
    //         totalSize,
    //         totalMaxSize,
    //         totalMemoryUsage,
    //         tierStats,
    //         hitRate
    //     };
    // }

    // /**
    //  * 获取总创建数量
    //  */
    // private getTotalCreated(): number {
    //     return this.pools.reduce((total, pool) => total + pool.getStats().totalCreated, 0);
    // }
}