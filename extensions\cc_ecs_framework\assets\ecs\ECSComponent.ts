import { IComponent } from "../types";
import { ECSEntity } from "./ECSEntity";

/**
 * <AUTHOR>
 * @data 2025-08-07 16:25
 * @filePath extensions\cc_ecs_framework\assets\ECS\ECSComponent.ts
 * @description 游戏组件基类
 *
 * ECS架构中的组件（Component），用于实现具体的游戏功能。
 * 组件包含数据和行为，可以被添加到实体上以扩展实体的功能。
 */
export abstract class ECSComponent implements IComponent {
    /**
     * 组件ID生成器
     * 
     * 用于为每个组件分配唯一的ID。
     */
    private static _idGenerator: number = 0;
    /**
     * 组件唯一标识符
     * 
     * 在整个游戏生命周期中唯一的数字ID。
     */
    public readonly id: number;
    /**
     * 组件所属的实体
     * 
     * 指向拥有此组件的实体实例。
     */
    private entity!: ECSEntity;
    /**
     * 组件启用状态
     * 
     * 控制组件是否参与更新循环。
     */
    private _enabled: boolean = true;
    /**
     * 获取组件启用状态
     * 
     * 组件的实际启用状态取决于自身状态和所属实体的状态。
     * 
     * @returns 如果组件和所属实体都启用则返回true
     */
    public get enabled(): boolean {
        return this.entity ? this.entity.enabled && this._enabled : this._enabled;
    }
    /**
     * 更新顺序
     * 
     * 决定组件在更新循环中的执行顺序。
     */
    private _updateOrder: number = 0;
    /** 更新顺序 */
    public get updateOrder(): number { return this._updateOrder; }
    public set updateOrder(value: number) { this._updateOrder = value; }
    /**
     * 创建组件实例
     * 
     * 自动分配唯一ID给组件。
     */
    constructor() {
        this.id = ECSComponent._idGenerator++;
    }

    // /**
    //  * 设置组件启用状态
    //  * 
    //  * 当状态改变时会触发相应的生命周期回调。
    //  * 
    //  * @param value - 新的启用状态
    //  */
    // public set enabled(value: boolean) {
    //     if (this._enabled !== value) {
    //         this._enabled = value;
    //         if (this._enabled) {
    //             this.onEnabled();
    //         } else {
    //             this.onDisabled();
    //         }
    //     }
    // }

}