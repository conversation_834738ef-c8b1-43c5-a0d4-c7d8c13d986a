import { Component } from "db://framework/ECS/Component";
export enum CollectibleType {
    AIR_STRIKE = 'air_strike'
}
/**
 * <AUTHOR>
 * @data 2025-08-04 21:55
 * @filePath assets\lawnMowerDemo_ECS\scripts\components\Collectible.ts
 * @description 
 */
export class Collectible extends Component {

    public type: CollectibleType;
    public value: number = 1;
    public isCollected: boolean = false;

    constructor(type: CollectibleType, value: number = 1) {
        super();
        this.type = type;
        this.value = value;
    }

    public collect(): void {
        this.isCollected = true;
    }

}