import { ComponentType, IComponentIndex, IndexStats, IndexType } from ".";
import { ECSEntity } from "../../ECSEntity";

/**
 * <AUTHOR>
 * @data 2025-08-07 16:42
 * @filePath extensions\cc_ecs_framework\assets\ECS\core\components\HashComponentIndex.ts
 * @description 哈希索引实现
 *
 * 使用Map数据结构，提供O(1)的查找性能。
 * 适合大多数查询场景。
 */
export class HashComponentIndex implements IComponentIndex {
    public readonly type = IndexType.HASH;

    private _componentToEntities = new Map<ComponentType, Set<ECSEntity>>();
    private _entityToComponents = new Map<ECSEntity, Set<ComponentType>>();
    private _queryCount = 0;
    private _totalQueryTime = 0;
    private _lastUpdated = Date.now();

    private _setPool: Set<ECSEntity>[] = [];
    private _componentTypeSetPool: Set<ComponentType>[] = [];
    public addEntity(entity: ECSEntity): void {
        if (entity.components.length === 0) {
            const componentTypes = this._componentTypeSetPool.pop() || new Set<ComponentType>();
            componentTypes.clear();
            this._entityToComponents.set(entity, componentTypes);
            this._lastUpdated = Date.now();
            return;
        }

        const componentTypes = this._componentTypeSetPool.pop() || new Set<ComponentType>();
        componentTypes.clear();

        for (const component of entity.components) {
            const componentType = component.constructor as ComponentType;
            componentTypes.add(componentType);

            let entities = this._componentToEntities.get(componentType);
            if (!entities) {
                entities = this._setPool.pop() || new Set<ECSEntity>();
                entities.clear();
                this._componentToEntities.set(componentType, entities);
            }
            entities.add(entity);
        }

        this._entityToComponents.set(entity, componentTypes);
        this._lastUpdated = Date.now();
    }
    public removeEntity(entity: ECSEntity): void {
        const componentTypes = this._entityToComponents.get(entity);
        if (!componentTypes) return;

        for (const componentType of componentTypes) {
            const entities = this._componentToEntities.get(componentType);
            if (entities) {
                entities.delete(entity);
                if (entities.size === 0) {
                    this._componentToEntities.delete(componentType);
                    if (this._setPool.length < 50) {
                        entities.clear();
                        this._setPool.push(entities);
                    }
                }
            }
        }

        this._entityToComponents.delete(entity);

        if (this._componentTypeSetPool.length < 50) {
            componentTypes.clear();
            this._componentTypeSetPool.push(componentTypes);
        }

        this._lastUpdated = Date.now();
    }
    public query(componentType: ComponentType): Set<ECSEntity> {
        const startTime = performance.now();
        const entities = this._componentToEntities.get(componentType);
        const result = entities ? new Set(entities) : new Set<ECSEntity>();

        this._queryCount++;
        this._totalQueryTime += performance.now() - startTime;

        return result;
    }
    public queryMultiple(componentTypes: ComponentType[], operation: 'AND' | 'OR'): Set<ECSEntity> {
        const startTime = performance.now();

        if (componentTypes.length === 0) {
            return new Set();
        }

        if (componentTypes.length === 1) {
            return this.query(componentTypes[0]);
        }

        let result: Set<ECSEntity>;

        if (operation === 'AND') {
            let smallestSet: Set<ECSEntity> | undefined;
            let smallestSize = Infinity;

            for (const componentType of componentTypes) {
                const entities = this._componentToEntities.get(componentType);
                if (!entities || entities.size === 0) {
                    this._queryCount++;
                    this._totalQueryTime += performance.now() - startTime;
                    return new Set();
                }
                if (entities.size < smallestSize) {
                    smallestSize = entities.size;
                    smallestSet = entities;
                }
            }

            result = new Set();
            if (smallestSet) {
                for (const entity of smallestSet) {
                    let hasAll = true;
                    for (const componentType of componentTypes) {
                        const entities = this._componentToEntities.get(componentType);
                        if (!entities || !entities.has(entity)) {
                            hasAll = false;
                            break;
                        }
                    }
                    if (hasAll) {
                        result.add(entity);
                    }
                }
            }
        } else {
            result = new Set();
            for (const componentType of componentTypes) {
                const entities = this._componentToEntities.get(componentType);
                if (entities) {
                    for (const entity of entities) {
                        result.add(entity);
                    }
                }
            }
        }

        this._queryCount++;
        this._totalQueryTime += performance.now() - startTime;

        return result;
    }
    public clear(): void {
        this._componentToEntities.clear();
        this._entityToComponents.clear();
        this._lastUpdated = Date.now();
    }
    public getStats(): IndexStats {
        let memoryUsage = 0;

        memoryUsage += this._componentToEntities.size * 64;
        memoryUsage += this._entityToComponents.size * 64;

        for (const entities of this._componentToEntities.values()) {
            memoryUsage += entities.size * 8;
        }

        for (const components of this._entityToComponents.values()) {
            memoryUsage += components.size * 8;
        }

        return {
            type: this.type,
            size: this._componentToEntities.size,
            memoryUsage,
            queryCount: this._queryCount,
            avgQueryTime: this._queryCount > 0 ? this._totalQueryTime / this._queryCount : 0,
            lastUpdated: this._lastUpdated
        };
    }
}