import { ECSComponent } from "../../ECSComponent";
import { ECSEntity } from "../../ECSEntity";

/** 组件类型定义 */
export type ComponentType<T extends ECSComponent = ECSComponent> = new (...args: unknown[]) => T;
/** 组件索引类型 */
export enum IndexType {
    /** 哈希索引 - 最快查找 */
    HASH = 'hash',
    /** 位图索引 - 内存高效 */
    BITMAP = 'bitmap',
    /** 排序索引 - 支持范围查询 */
    SORTED = 'sorted'
}
/** 索引统计信息 */
export interface IndexStats {
    /** 索引类型 */
    type: IndexType;
    /** 索引大小 */
    size: number;
    /** 内存使用量（字节） */
    memoryUsage: number;
    /** 查询次数 */
    queryCount: number;
    /** 平均查询时间（毫秒） */
    avgQueryTime: number;
    /** 最后更新时间 */
    lastUpdated: number;
}
/** 组件索引接口 */
export interface IComponentIndex {
    /** 索引类型 */
    readonly type: IndexType;
    /** 添加实体到索引 */
    addEntity(entity: ECSEntity): void;
    /** 从索引中移除实体 */
    removeEntity(entity: ECSEntity): void;
    /** 查询包含指定组件的实体 */
    query(componentType: ComponentType): Set<ECSEntity>;
    /** 批量查询多个组件 */
    queryMultiple(componentTypes: ComponentType[], operation: 'AND' | 'OR'): Set<ECSEntity>;
    /** 清空索引 */
    clear(): void;
    /** 获取索引统计信息 */
    getStats(): IndexStats;
}